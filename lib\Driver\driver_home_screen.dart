import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sepesha_app/Driver/dasboard/presentation/dashboard_screen.dart';
import 'package:sepesha_app/Driver/history/presentation/history_screen.dart';
import 'package:sepesha_app/Driver/wallet/presentation/wallet_screen.dart';
import 'package:sepesha_app/provider/payment_provider.dart';

class MainLayout extends StatefulWidget {
  const MainLayout({super.key});

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const DashboardScreen(),
    const WalletScreen(),
    const HistoryScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
    automaticallyImplyLeading: false,
    title: Text(_getTitleForIndex(_currentIndex)),
    actions: _getActionsForIndex(_currentIndex),
  ),
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_balance_wallet),
            label: 'Wallet',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.history),
            label: 'Trips',
          ),
        ],
      ),
    );
  }
  String _getTitleForIndex(int index) {
    switch (index) {
      case 0: return 'Driver Dashboard';
      case 1: return 'Wallet';
      case 2: return 'Ride History';
      default: return 'Driver';
    }
  }

  List<Widget> _getActionsForIndex(int index) {
    switch (index) {
      case 0: // Dashboard
        return [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              setState(() => _currentIndex = 2); // Switch to history tab
            },
          ),
        ];
      case 1: // Wallet
        return [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<PaymentProvider>(
                context,
                listen: false,
              ).refreshWalletBalance();
            },
          ),
        ];
      case 2: // History
        return [];
      default:
        return [];
    }
  }
}