[   +2 ms] W/ProxyAndroidLoggerBackend( 8009): Too many Flogger logs received before configuration. Dropping old logs.
[   +1 ms] E/flutter ( 8009): [ERROR:flutter/fml/platform/android/jni_util.cc(206)] java.lang.IllegalStateException: Image is
already closed
[   +6 ms] E/flutter ( 8009):   at android.media.Image.throwISEIfImageIsInvalid(Image.java:73)
[  +13 ms] E/flutter ( 8009):   at android.media.ImageReader$SurfaceImage.getHardwareBuffer(ImageReader.java:989)
[   +3 ms] E/flutter ( 8009): 
[   +5 ms] F/flutter ( 8009): [FATAL:flutter/shell/platform/android/platform_view_android_jni_impl.cc(1600)] Check failed:
fml::jni::CheckException(env). 
[  +14 ms] E/PlatformViewWrapper( 8009): Platform view cannot be composed without a valid RenderTarget surface.
[   +4 ms] F/libc    ( 8009): Fatal signal 6 (SIGABRT), code -1 (SI_QUEUE) in tid 8045 (1.raster), pid 8009 (mpany.sepeshapp)
[+2751 ms] *** *** *** *** *** *** *** *** *** *** *** *** *** *** *** ***
[   +1 ms] Build fingerprint: 'google/sargo/sargo:12/SP2A.220505.008/8782922:user/release-keys'
[   +1 ms] Revision: 'MP1.0'
[   +1 ms] ABI: 'arm64'
[   +1 ms] Timestamp: 2025-07-22 04:54:01.*********+0300
[   +1 ms] Process uptime: 0s
[   +1 ms] Cmdline: com.sepeshacompany.sepeshapp
[   +1 ms] pid: 8009, tid: 8045, name: 1.raster  >>> com.sepeshacompany.sepeshapp <<<
[   +1 ms] uid: 10268
[   +2 ms] signal 6 (SIGABRT), code -1 (SI_QUEUE), fault addr --------
[        ] Abort message: '[FATAL:flutter/shell/platform/android/platform_view_android_jni_impl.cc(1600)] Check failed:      
fml::jni::CheckException(env). 
[   +1 ms] '
[   +1 ms]     x0  0000000000000000  x1  0000000000001f6d  x2  0000000000000006  x3  0000007486927620
[        ]     x4  6d602e6c716e6573  x5  6d602e6c716e6573  x6  6d602e6c716e6573  x7  7f7f7f7f7f7f7f7f
[   +3 ms]     x8  00000000000000f0  x9  0147a6837414f020  x10 0000000000000000  x11 ffffff80fffffbdf
[   +2 ms]     x12 0000000000000001  x13 000000000000007e  x14 00000074869264d0  x15 0004d034764f643f
[   +1 ms]     x16 00000077be290050  x17 00000077be26cdb0  x18 0000007485f00000  x19 0000000000001f49
[   +1 ms]     x20 0000000000001f6d  x21 00000000ffffffff  x22 0000007486929000  x23 0000000000000000
[   +1 ms]     x24 0000000000000000  x25 00000075c8533e30  x26 0000000000000000  x27 00000075b853b0a8
[   +1 ms]     x28 0000000000000000  x29 00000074869276a0
[   +1 ms]     lr  00000077be21faa0  sp  0000007486927600  pc  00000077be21facc  pst 0000000000000000
[   +1 ms] backtrace:
[   +2 ms]       #00 pc 000000000004facc  /apex/com.android.runtime/lib64/bionic/libc.so (abort+164) (BuildId:
cd7952cb40d1a2deca6420c2da7910be)
[   +3 ms]       #01 pc 0000000001d8a480
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +4 ms]       #02 pc 0000000001d8a43c
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +2 ms]       #03 pc 0000000001d6ae24
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +2 ms]       #04 pc 0000000001d62724
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +2 ms]       #05 pc 0000000001d63bd8
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +3 ms]       #06 pc 0000000001d624f0
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +4 ms]       #07 pc 00000000020bce58
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +5 ms]       #08 pc 00000000020b7730
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #09 pc 00000000020bd120
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #10 pc 00000000020b7730
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #11 pc 00000000020bd120
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #12 pc 00000000020b7730
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #13 pc 00000000020bd120
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #14 pc 00000000020b7730
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +2 ms]       #15 pc 00000000020bd120
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #16 pc 00000000020b7730
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +3 ms]       #17 pc 00000000020bb688
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +3 ms]       #18 pc 00000000020b3e60
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +4 ms]       #19 pc 00000000020b3d50
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #20 pc 00000000021e3498
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #21 pc 00000000021e309c
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #22 pc 00000000021e1f30
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #23 pc 00000000021e290c
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #24 pc 00000000021e4538
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +2 ms]       #25 pc 00000000021e23a0
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +2 ms]       #26 pc 00000000021e2138
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #27 pc 00000000021f0bb4
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #28 pc 0000000001d8b03c
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +2 ms]       #29 pc 0000000001d90b34
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +1 ms]       #30 pc 0000000000016904  /system/lib64/libutils.so (android::Looper::pollInner(int)+912) (BuildId:
c1f7ebcd9c7d12c6107c4834ed56b006)
[   +1 ms]       #31 pc 000000000001650c  /system/lib64/libutils.so (android::Looper::pollOnce(int, int*, int*, void**)+112)
(BuildId: c1f7ebcd9c7d12c6107c4834ed56b006)
[   +2 ms]       #32 pc 0000000000017160  /system/lib64/libandroid.so (ALooper_pollOnce+100) (BuildId:
d99f026376cf49597368537f0f1e35ab)
[   +1 ms]       #33 pc 0000000001d90abc
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +3 ms]       #34 pc 0000000001d8af88
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +2 ms]       #35 pc 0000000001d8ee00
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +2 ms]       #36 pc 0000000001d8ec98
/data/app/~~rQML3gdixsKXp4sn7u_qrA==/com.sepeshacompany.sepeshapp-F8fnRD6b9IQgwr-uadKyeg==/lib/arm64/libflutter.so (BuildId: 
91641331505719b81fd8145b89c7fb6e6e8f04da)
[   +3 ms]       #37 pc 00000000000b1810  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start(void*)+264)        
(BuildId: cd7952cb40d1a2deca6420c2da7910be)
[   +1 ms]       #38 pc 00000000000512f0  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId:
cd7952cb40d1a2deca6420c2da7910be)
[   +6 ms] Service protocol connection closed.
[   +3 ms] Lost connection to device.
[   +5 ms] DevFS: Deleting filesystem on the device
(file:///data/user/0/com.sepeshacompany.sepeshapp/code_cache/sepeshaNZMDXD/sepesha/)
[  +12 ms] DevFS: Deleted filesystem on the device
(file:///data/user/0/com.sepeshacompany.sepeshapp/code_cache/sepeshaNZMDXD/sepesha/)
[  +23 ms] "flutter run" took 274,366ms.
[ +138 ms] Running 3 shutdown hooks
[  +17 ms] Shutdown hooks complete
[ +269 ms] exiting with code 0
PS C:\Users\<USER>\Desktop\sepesha> 