import 'package:flutter/material.dart';
import 'package:sepesha_app/Utilities/app_color.dart';
import 'package:sepesha_app/Utilities/app_text_style.dart';
import 'package:sepesha_app/services/session_manager.dart';
import 'package:sepesha_app/services/preferences.dart';

class AppDrawerHeader extends StatefulWidget {
  const AppDrawerHeader({super.key});

  @override
  State<AppDrawerHeader> createState() => _AppDrawerHeaderState();
}

class _AppDrawerHeaderState extends State<AppDrawerHeader> {
  String? firstName;
  String? lastName;
  String? email;
  String? profilePhotoUrl;
  double? userRating;
  int? totalRides;
  String? userType;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      firstName = await Preferences.instance.firstName;
      lastName = await Preferences.instance.lastName;
      email = await Preferences.instance.email;

      final sessionUser = SessionManager.instance.user;
      if (sessionUser != null) {
        userType = sessionUser.userType;
        userRating = sessionUser.averageRating;
        totalRides = sessionUser.totalRides;
        profilePhotoUrl = sessionUser.profilePhotoUrl;
      }

      setState(() {});
    } catch (e) {
      debugPrint('Error loading user data for drawer: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final fullName = '${firstName ?? ''} ${lastName ?? ''}'.trim();
    final displayName = fullName.isNotEmpty ? fullName : 'User';

    return DrawerHeader(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColor.primary, AppColor.primary.withOpacity(0.8)],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 35,
                backgroundColor: AppColor.white,
                backgroundImage:
                    profilePhotoUrl != null
                        ? NetworkImage(profilePhotoUrl!)
                        : null,
                child:
                    profilePhotoUrl == null
                        ? Icon(Icons.person, size: 40, color: AppColor.primary)
                        : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      displayName,
                      style: AppTextStyle.heading3(AppColor.white),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    if (email != null)
                      Text(
                        email!,
                        style: AppTextStyle.paragraph2(
                          AppColor.white.withOpacity(0.9),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              if (userType == 'driver' && userRating != null) ...[
                Icon(Icons.star, color: Colors.amber, size: 20),
                const SizedBox(width: 4),
                Text(
                  userRating!.toStringAsFixed(1),
                  style: AppTextStyle.paragraph1(AppColor.white),
                ),
                const SizedBox(width: 16),
              ],
              if (totalRides != null) ...[
                Icon(Icons.directions_car, color: AppColor.white, size: 20),
                const SizedBox(width: 4),
                Text(
                  '$totalRides rides',
                  style: AppTextStyle.paragraph1(AppColor.white),
                ),
              ],
              const Spacer(),
              if (userType != null)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColor.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    userType!.toUpperCase(),
                    style: AppTextStyle.paragraph2(AppColor.white),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
