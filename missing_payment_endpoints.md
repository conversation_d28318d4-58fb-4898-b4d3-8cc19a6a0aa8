# Missing Payment Endpoints for Production-Ready Payment System

## Critical Payment Endpoints Needed in Backend

### 1. Payment Processing
```
POST /process-payment
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "booking_id": "booking_uuid",
  "payment_method": "cash|wallet|card|bank",
  "amount": 15000.00,
  "currency": "TZS",
  "payment_details": {
    // For card payments
    "card_token": "encrypted_card_token",
    // For mobile money
    "phone_number": "************",
    // For bank transfer
    "bank_account": "account_details"
  }
}

Response:
{
  "status": true,
  "message": "Payment processed successfully",
  "data": {
    "transaction_id": "txn_uuid",
    "payment_status": "success|pending|failed",
    "amount": 15000.00,
    "currency": "TZS",
    "payment_method": "wallet",
    "receipt_url": "https://receipts.sepesha.com/txn_uuid.pdf",
    "processed_at": "2024-01-01T12:00:00Z"
  }
}
```

### 2. Wallet Management
```
POST /wallet/add-funds
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "amount": 50000.00,
  "currency": "TZS",
  "payment_source": "card|bank|mobile_money",
  "source_details": {
    "card_token": "encrypted_token",
    "phone_number": "************"
  }
}

Response:
{
  "status": true,
  "message": "Funds added successfully",
  "data": {
    "transaction_id": "txn_uuid",
    "new_balance_tzs": "75000.00",
    "new_balance_usd": "30.00",
    "amount_added": 50000.00,
    "currency": "TZS"
  }
}
```

```
POST /wallet/withdraw
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "amount": 25000.00,
  "currency": "TZS",
  "withdrawal_method": "bank|mobile_money",
  "destination_details": {
    "bank_account": "account_number",
    "phone_number": "************"
  }
}

Response:
{
  "status": true,
  "message": "Withdrawal initiated",
  "data": {
    "transaction_id": "txn_uuid",
    "status": "pending|completed",
    "amount": 25000.00,
    "new_balance_tzs": "50000.00",
    "processing_fee": 1000.00
  }
}
```

### 3. Payment History
```
GET /payment-history?page=1&limit=20&status=all&type=all
Authorization: Bearer token

Response:
{
  "status": true,
  "message": "Payment history retrieved",
  "data": {
    "transactions": [
      {
        "id": "txn_uuid",
        "type": "payment|top_up|withdrawal|refund",
        "amount": 15000.00,
        "currency": "TZS",
        "status": "success|pending|failed",
        "payment_method": "wallet",
        "description": "Ride payment for booking #12345",
        "booking_id": "booking_uuid",
        "created_at": "2024-01-01T12:00:00Z",
        "receipt_url": "https://receipts.sepesha.com/txn_uuid.pdf"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_transactions": 95
    }
  }
}
```

### 4. Payment Validation
```
POST /payment/validate
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "payment_method": "wallet|card|bank",
  "amount": 15000.00,
  "currency": "TZS"
}

Response:
{
  "status": true,
  "message": "Payment method validated",
  "data": {
    "is_valid": true,
    "validation_details": {
      "sufficient_balance": true,
      "method_available": true,
      "estimated_processing_time": "instant",
      "processing_fee": 0.00
    }
  }
}
```

### 5. Refund Processing
```
POST /payment/refund
Authorization: Bearer token
Content-Type: application/json

Request:
{
  "transaction_id": "original_txn_uuid",
  "amount": 15000.00,
  "reason": "Trip cancelled by driver",
  "refund_method": "original|wallet"
}

Response:
{
  "status": true,
  "message": "Refund processed",
  "data": {
    "refund_id": "refund_uuid",
    "original_transaction_id": "original_txn_uuid",
    "refund_amount": 15000.00,
    "refund_status": "pending|completed",
    "estimated_completion": "2024-01-01T12:30:00Z"
  }
}
```

### 6. Available Payment Methods
```
GET /payment-methods/available
Authorization: Bearer token

Response:
{
  "status": true,
  "message": "Available payment methods",
  "data": {
    "methods": [
      {
        "type": "cash",
        "name": "Cash Payment",
        "is_available": true,
        "processing_fee": 0.00
      },
      {
        "type": "wallet",
        "name": "Sepesha Wallet",
        "is_available": true,
        "current_balance": 50000.00,
        "processing_fee": 0.00
      },
      {
        "type": "card",
        "name": "Credit/Debit Card",
        "is_available": true,
        "processing_fee": 500.00,
        "supported_cards": ["visa", "mastercard"]
      },
      {
        "type": "mobile_money",
        "name": "Mobile Money (Zenopay)",
        "is_available": true,
        "processing_fee": 200.00,
        "supported_providers": ["vodacom", "airtel", "tigo"]
      }
    ]
  }
}
```

## Integration with Existing Endpoints

These new endpoints would work alongside the existing:
- `/user/update-profile/{id}` - For setting preferred payment method
- User profile response - For wallet balance display

## Security Requirements

1. **Payment Data Encryption**: All payment details must be encrypted
2. **PCI Compliance**: Card data handling must be PCI compliant
3. **Transaction Logging**: All payment attempts must be logged
4. **Fraud Detection**: Implement basic fraud detection mechanisms
5. **Rate Limiting**: Prevent payment spam/abuse

## Third-Party Integrations Needed

1. **Zenopay API**: For mobile money processing
2. **Card Payment Gateway**: For credit/debit card processing
3. **Bank Transfer API**: For bank transfer processing
4. **Receipt Generation**: PDF receipt generation service
